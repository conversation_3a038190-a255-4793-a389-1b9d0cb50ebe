package cz.deuss.userservice.service

import cz.deuss.userservice.database.model.InviteToken
import java.util.UUID

/**
 * Interface for validating invite tokens.
 * Provides different implementations for production and local development environments.
 */
interface InviteTokenValidator {
    
    /**
     * Validates an invite token and returns it if valid.
     * 
     * @param tokenId The UUID of the token to validate
     * @return The validated InviteToken
     * @throws NotFoundException if the token is invalid
     */
    fun findAndValidateToken(tokenId: UUID): InviteToken
    
    /**
     * Marks a token as used by a specific user.
     * 
     * @param tokenId The UUID of the token to mark as used
     * @param userId The UUID of the user who used the token
     */
    fun useToken(tokenId: UUID, userId: UUID)
    
    /**
     * Verifies if a token is valid without returning the token object.
     * Used by the verification endpoint.
     * 
     * @param tokenId The UUID of the token to verify
     * @throws NotFoundException if the token is invalid
     */
    fun verifyToken(tokenId: UUID)
}
