package cz.deuss.userservice.service

import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.userservice.database.model.InviteToken
import cz.deuss.userservice.database.repository.InviteTokenRepository
import cz.deuss.userservice.database.repository.UserRepository
import cz.deuss.userservice.domain.user.UserId
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.context.annotation.Value
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Singleton
import java.time.Duration
import java.time.LocalDateTime
import java.util.UUID
import kotlin.jvm.optionals.getOrNull

/**
 * Service for persisting, revoking and validating invite tokens
 */
@Singleton
open class InviteTokenPersistenceService(
    private val inviteTokenRepository: InviteTokenRepository,
    @Value("\${micronaut.security.token.invite-token.generator.expiration}")
    private val inviteTokenExpiry: Duration,
    private val userRepository: UserRepository,
    private val inviteTokenValidator: InviteTokenValidator,
) {

    private val logger = KotlinLogging.logger {}

    /**
     * Persist a new invite token when it's generated
     */
    fun createTokens(id: UserId, count: Int): List<UUID> {
        val user = userRepository.getById(id.id)
        logger.info { "User#${user?.id} is creating $count new invite tokens." }
        val expiresAt = LocalDateTime.now().plus(inviteTokenExpiry)

        val tokens = List (count) {
            InviteToken(
                createdByUser = user,
                expiresAt = expiresAt,
            )
        }
        return inviteTokenRepository.saveAll(tokens).map { it.id }
    }

    @Transactional
    open fun revokeToken(tokenId: UUID) {
        val token = inviteTokenValidator.findAndValidateToken(tokenId)
        token.revoke()
        inviteTokenRepository.update(token)
    }

    @Transactional
    open fun useToken(tokenId: UUID, userId: UUID) {
        inviteTokenValidator.useToken(tokenId, userId)
    }

    fun findAndValidateToken(tokenId: UUID): InviteToken {
        return inviteTokenValidator.findAndValidateToken(tokenId)
    }
}
