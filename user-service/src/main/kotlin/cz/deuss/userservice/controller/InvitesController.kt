package cz.deuss.userservice.controller

import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.platform.offchain.framework.authentication.AuthenticatedUserId
import cz.deuss.userservice.api.InvitesManagementApi
import cz.deuss.userservice.api.model.InviteTokenGenerateRequest
import cz.deuss.userservice.api.model.InviteTokenGenerateResponse
import cz.deuss.userservice.domain.user.toUserId
import cz.deuss.userservice.service.InviteTokenPersistenceService
import cz.deuss.userservice.service.InviteTokenValidator
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.http.HttpResponse
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.annotation.ExecuteOn
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import java.util.UUID


/**
 * Controller for token-related operations
 */
@ExposedController
@ExecuteOn(TaskExecutors.BLOCKING)
@Secured(SecurityRule.IS_AUTHENTICATED)
open class InvitesController(
    private val tokenService: InviteTokenPersistenceService,
    private val inviteTokenValidator: InviteTokenValidator,
    private val authenticatedUserId: () -> AuthenticatedUserId,
) : InvitesManagementApi {

    private val logger = KotlinLogging.logger {}

    override fun generateInviteToken(inviteTokenGenerateRequest: InviteTokenGenerateRequest): InviteTokenGenerateResponse {
        val auth = authenticatedUserId()
        val tokens = tokenService.createTokens(auth.toUserId(), inviteTokenGenerateRequest.count)
        return InviteTokenGenerateResponse(inviteTokens = tokens)
    }

    override fun revokeInviteToken(token: UUID): HttpResponse<Void> {
        val auth = authenticatedUserId()
        logger.info { "User#${auth.id} is revoking InviteToken#$token" }

        tokenService.revokeToken(token)
        return HttpResponse.noContent()
    }

    @Secured(SecurityRule.IS_ANONYMOUS)
    override fun verifyInviteToken(body: UUID) {
        inviteTokenValidator.verifyToken(body)
    }
}
